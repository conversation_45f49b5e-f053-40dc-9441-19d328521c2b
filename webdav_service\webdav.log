[2025-08-02 07:31:00,167: WARNING/ForkPoolWorker-1] Upload attempt 1 failed: Remote parent for: /GEMINI/Love Sick/Body On Me feat PENOMECO.flac not found
[2025-08-02 07:31:00,213: WARNING/ForkPoolWorker-1] Upload attempt 2 failed: Remote parent for: /GEMINI/Love Sick/Body On Me feat PENOMECO.flac not found
[2025-08-02 07:31:00,258: WARNING/ForkPoolWorker-1] Upload attempt 3 failed: Remote parent for: /GEMINI/Love Sick/Body On Me feat PENOMECO.flac not found
[2025-08-02 07:31:00,258: ERROR/ForkPoolWorker-1] Failed to upload file to WebDAV: Remote parent for: /GEMINI/Love Sick/Body On Me feat PENOMECO.flac not found
[2025-08-02 07:31:00,258: ERROR/ForkPoolWorker-1] Upload task 2356 failed: Remote parent for: /GEMINI/Love Sick/Body On Me feat PENOMECO.flac not found
[2025-08-02 07:31:01,026: ERROR/ForkPoolWorker-1] Task app.upload_task[webdav_upload_2356] raised unexpected: RemoteParentNotFound('/GEMINI/Love Sick/Body On Me feat PENOMECO.flac')
Traceback (most recent call last):
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 ~~~^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           ~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/app/app.py", line 265, in upload_task
    raise e
  File "/app/app.py", line 244, in upload_task
    upload_to_webdav(
    ~~~~~~~~~~~~~~~~^
        transcoded_file_path,
        ^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
        format_type
        ^^^^^^^^^^^
    )
    ^
  File "/app/utils.py", line 473, in upload_to_webdav
    raise e
  File "/app/utils.py", line 467, in upload_to_webdav
    raise e
  File "/app/utils.py", line 458, in upload_to_webdav
    client.upload_sync(remote_path=remote_path, local_path=file_path)
    ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/webdav3/client.py", line 663, in upload_sync
    self.upload(local_path=local_path, remote_path=remote_path, progress=progress, progress_args=progress_args)
    ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/webdav3/client.py", line 562, in upload
    self.upload_file(local_path=local_path, remote_path=remote_path, progress_args=progress_args)
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/webdav3/client.py", line 67, in _wrapper
    res = fn(self, *args, **kw)
  File "/usr/local/lib/python3.13/site-packages/webdav3/client.py", line 626, in upload_file
    raise RemoteParentNotFound(urn.path())
webdav3.exceptions.RemoteParentNotFound: Remote parent for: /GEMINI/Love Sick/Body On Me feat PENOMECO.flac not found
[2025-08-02 07:43:58,140: WARNING/ForkPoolWorker-1] Upload attempt 1 failed: Remote resource: /Rich%20Brian-Dat%20tick%20Remix%20feat.%20Ghostface%20Killah%20Pouya.flac not found
[2025-08-02 07:43:58,725: WARNING/ForkPoolWorker-1] Upload attempt 2 failed: Remote resource: /Rich%20Brian-Dat%20tick%20Remix%20feat.%20Ghostface%20Killah%20Pouya.flac not found
[2025-08-02 07:43:59,139: WARNING/ForkPoolWorker-1] Upload attempt 3 failed: Remote resource: /Rich%20Brian-Dat%20tick%20Remix%20feat.%20Ghostface%20Killah%20Pouya.flac not found
[2025-08-02 07:43:59,139: ERROR/ForkPoolWorker-1] Failed to upload file to WebDAV: Remote resource: /Rich%20Brian-Dat%20tick%20Remix%20feat.%20Ghostface%20Killah%20Pouya.flac not found
[2025-08-02 07:43:59,139: ERROR/ForkPoolWorker-1] Upload task 2357 failed: Remote resource: /Rich%20Brian-Dat%20tick%20Remix%20feat.%20Ghostface%20Killah%20Pouya.flac not found
[2025-08-02 07:43:59,924: ERROR/ForkPoolWorker-1] Task app.upload_task[webdav_upload_2357] raised unexpected: NotFound()
Traceback (most recent call last):
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 ~~~^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           ~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/app/app.py", line 265, in upload_task
    raise e
  File "/app/app.py", line 244, in upload_task
    upload_to_webdav(
    ~~~~~~~~~~~~~~~~^
        transcoded_file_path,
        ^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
        format_type
        ^^^^^^^^^^^
    )
    ^
  File "/app/utils.py", line 469, in upload_to_webdav
    raise e
  File "/app/utils.py", line 463, in upload_to_webdav
    raise e
  File "/app/utils.py", line 454, in upload_to_webdav
    client.upload_sync(remote_path=remote_path, local_path=file_path)
    ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/webdav3/client.py", line 663, in upload_sync
    self.upload(local_path=local_path, remote_path=remote_path, progress=progress, progress_args=progress_args)
    ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/webdav3/client.py", line 562, in upload
    self.upload_file(local_path=local_path, remote_path=remote_path, progress_args=progress_args)
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/webdav3/client.py", line 67, in _wrapper
    res = fn(self, *args, **kw)
  File "/usr/local/lib/python3.13/site-packages/webdav3/client.py", line 646, in upload_file
    self.execute_request(action='upload', path=urn.quote(), data=local_file)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/webdav3/client.py", line 226, in execute_request
    raise RemoteResourceNotFound(path=path)
webdav3.exceptions.NotFound
[2025-08-02 08:03:15,960: WARNING/ForkPoolWorker-1] Upload attempt 1 failed: Remote resource: /%E6%A2%81%E5%87%A1-%E5%AE%B6%E5%9C%A8%E5%B1%B1%E6%B5%B7%E9%96%93.flac not found
[2025-08-02 08:03:16,506: WARNING/ForkPoolWorker-1] Upload attempt 2 failed: Remote resource: /%E6%A2%81%E5%87%A1-%E5%AE%B6%E5%9C%A8%E5%B1%B1%E6%B5%B7%E9%96%93.flac not found
[2025-08-02 08:03:16,929: WARNING/ForkPoolWorker-1] Upload attempt 3 failed: Remote resource: /%E6%A2%81%E5%87%A1-%E5%AE%B6%E5%9C%A8%E5%B1%B1%E6%B5%B7%E9%96%93.flac not found
[2025-08-02 08:03:16,929: ERROR/ForkPoolWorker-1] Failed to upload file to WebDAV: Remote resource: /%E6%A2%81%E5%87%A1-%E5%AE%B6%E5%9C%A8%E5%B1%B1%E6%B5%B7%E9%96%93.flac not found
[2025-08-02 08:03:16,929: ERROR/ForkPoolWorker-1] Upload task 2358 failed: Remote resource: /%E6%A2%81%E5%87%A1-%E5%AE%B6%E5%9C%A8%E5%B1%B1%E6%B5%B7%E9%96%93.flac not found
[2025-08-02 08:03:17,700: ERROR/ForkPoolWorker-1] Task app.upload_task[webdav_upload_2358] raised unexpected: NotFound()
Traceback (most recent call last):
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 ~~~^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           ~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/app/app.py", line 265, in upload_task
    raise e
  File "/app/app.py", line 244, in upload_task
    upload_to_webdav(
    ~~~~~~~~~~~~~~~~^
        transcoded_file_path,
        ^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
        format_type
        ^^^^^^^^^^^
    )
    ^
  File "/app/utils.py", line 469, in upload_to_webdav
    raise e
  File "/app/utils.py", line 463, in upload_to_webdav
    raise e
  File "/app/utils.py", line 454, in upload_to_webdav
    client.upload_sync(remote_path=remote_path, local_path=file_path)
    ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/webdav3/client.py", line 663, in upload_sync
    self.upload(local_path=local_path, remote_path=remote_path, progress=progress, progress_args=progress_args)
    ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/webdav3/client.py", line 562, in upload
    self.upload_file(local_path=local_path, remote_path=remote_path, progress_args=progress_args)
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/webdav3/client.py", line 67, in _wrapper
    res = fn(self, *args, **kw)
  File "/usr/local/lib/python3.13/site-packages/webdav3/client.py", line 646, in upload_file
    self.execute_request(action='upload', path=urn.quote(), data=local_file)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/webdav3/client.py", line 226, in execute_request
    raise RemoteResourceNotFound(path=path)
webdav3.exceptions.NotFound
[2025-08-02 08:17:27,512: WARNING/ForkPoolWorker-2] Upload attempt 1 failed: WebDAV path not found. Check the URL.
[2025-08-02 08:17:30,390: WARNING/ForkPoolWorker-2] Upload attempt 2 failed: WebDAV path not found. Check the URL.
[2025-08-02 08:17:33,196: WARNING/ForkPoolWorker-2] Upload attempt 3 failed: WebDAV path not found. Check the URL.
[2025-08-02 08:17:33,196: ERROR/ForkPoolWorker-2] Failed to upload file to WebDAV: WebDAV path not found. Check the URL.
[2025-08-02 08:17:33,196: ERROR/ForkPoolWorker-2] Upload task 2359 failed: WebDAV path not found. Check the URL.
[2025-08-02 08:17:34,032: ERROR/ForkPoolWorker-2] Task app.upload_task[webdav_upload_2359] raised unexpected: Exception('WebDAV path not found. Check the URL.')
Traceback (most recent call last):
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 ~~~^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           ~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/app/app.py", line 265, in upload_task
    raise e
  File "/app/app.py", line 244, in upload_task
    upload_to_webdav(
    ~~~~~~~~~~~~~~~~^
        transcoded_file_path,
        ^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
        format_type
        ^^^^^^^^^^^
    )
    ^
  File "/app/utils.py", line 508, in upload_to_webdav
    raise e
  File "/app/utils.py", line 498, in upload_to_webdav
    raise e
  File "/app/utils.py", line 474, in upload_to_webdav
    raise Exception("WebDAV path not found. Check the URL.")
Exception: WebDAV path not found. Check the URL.
[2025-08-02 08:27:29,357: WARNING/ForkPoolWorker-2] Upload attempt 1 failed: WebDAV path not found. Check the URL.
[2025-08-02 08:27:32,900: WARNING/ForkPoolWorker-2] Upload attempt 2 failed: WebDAV path not found. Check the URL.
[2025-08-02 08:27:35,950: WARNING/ForkPoolWorker-2] Upload attempt 3 failed: WebDAV path not found. Check the URL.
[2025-08-02 08:27:35,950: ERROR/ForkPoolWorker-2] Failed to upload file to WebDAV: WebDAV path not found. Check the URL.
[2025-08-02 08:27:35,950: ERROR/ForkPoolWorker-2] Upload task 2360 failed: WebDAV path not found. Check the URL.
[2025-08-02 08:27:36,899: ERROR/ForkPoolWorker-2] Task app.upload_task[webdav_upload_2360] raised unexpected: Exception('WebDAV path not found. Check the URL.')
Traceback (most recent call last):
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 ~~~^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           ~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/app/app.py", line 265, in upload_task
    raise e
  File "/app/app.py", line 244, in upload_task
    upload_to_webdav(
    ~~~~~~~~~~~~~~~~^
        transcoded_file_path,
        ^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
        format_type
        ^^^^^^^^^^^
    )
    ^
  File "/app/utils.py", line 508, in upload_to_webdav
    raise e
  File "/app/utils.py", line 498, in upload_to_webdav
    raise e
  File "/app/utils.py", line 474, in upload_to_webdav
    raise Exception("WebDAV path not found. Check the URL.")
Exception: WebDAV path not found. Check the URL.
[2025-08-02 09:08:05,322: ERROR/ForkPoolWorker-2] Failed to download original file for 82c9f98b536c3c6c6a2b488c286927bd9388ec162fbe0a39e1a53df03d93284e: ('Connection broken: IncompleteRead(0 bytes read, 5999516 more expected)', IncompleteRead(0 bytes read, 5999516 more expected))
[2025-08-02 09:08:05,323: ERROR/ForkPoolWorker-2] Upload task 2361 failed: Failed to download original file
[2025-08-02 09:08:06,095: ERROR/ForkPoolWorker-2] Task app.upload_task[webdav_upload_2361] raised unexpected: Exception('Failed to download original file')
Traceback (most recent call last):
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 ~~~^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           ~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/app/app.py", line 265, in upload_task
    raise e
  File "/app/app.py", line 217, in upload_task
    raise Exception("Failed to download original file")
Exception: Failed to download original file
[2025-08-02 09:08:24,350: ERROR/ForkPoolWorker-2] Failed to download original file for c7f4ff665735a6b9527197f5093cf7f37cd7b6392fc10c47134a1c7e06675a3a: ('Connection broken: IncompleteRead(0 bytes read, 8575506 more expected)', IncompleteRead(0 bytes read, 8575506 more expected))
[2025-08-02 09:08:24,351: ERROR/ForkPoolWorker-2] Upload task 2362 failed: Failed to download original file
[2025-08-02 09:08:25,136: ERROR/ForkPoolWorker-2] Task app.upload_task[webdav_upload_2362] raised unexpected: Exception('Failed to download original file')
Traceback (most recent call last):
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 ~~~^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           ~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/app/app.py", line 265, in upload_task
    raise e
  File "/app/app.py", line 217, in upload_task
    raise Exception("Failed to download original file")
Exception: Failed to download original file
[2025-08-02 09:10:39,234: ERROR/ForkPoolWorker-2] Failed to download original file for 56b78d4ee3a244caa8798ca68b140f6703ee199fc6ea68979000621ad9f017d3: ('Connection broken: IncompleteRead(0 bytes read, 6979552 more expected)', IncompleteRead(0 bytes read, 6979552 more expected))
[2025-08-02 09:10:39,235: ERROR/ForkPoolWorker-2] Upload task 2363 failed: Failed to download original file
[2025-08-02 09:10:40,029: ERROR/ForkPoolWorker-2] Task app.upload_task[webdav_upload_2363] raised unexpected: Exception('Failed to download original file')
Traceback (most recent call last):
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 ~~~^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           ~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/app/app.py", line 265, in upload_task
    raise e
  File "/app/app.py", line 217, in upload_task
    raise Exception("Failed to download original file")
Exception: Failed to download original file
[2025-08-02 09:12:43,288: ERROR/ForkPoolWorker-2] Failed to download original file for 3badbf919ae549968a255b11762f859ae7d4f8392b896c33a4ee48d3c47f28a6: ('Connection broken: IncompleteRead(0 bytes read, 7303091 more expected)', IncompleteRead(0 bytes read, 7303091 more expected))
[2025-08-02 09:12:43,288: ERROR/ForkPoolWorker-2] Upload task 2364 failed: Failed to download original file
[2025-08-02 09:12:44,159: ERROR/ForkPoolWorker-2] Task app.upload_task[webdav_upload_2364] raised unexpected: Exception('Failed to download original file')
Traceback (most recent call last):
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 ~~~^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           ~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/app/app.py", line 265, in upload_task
    raise e
  File "/app/app.py", line 217, in upload_task
    raise Exception("Failed to download original file")
Exception: Failed to download original file
[2025-08-02 09:12:47,768: ERROR/ForkPoolWorker-4] Failed to download original file for a2119710d8635fbb101581e445d8e3f18bf3517d04aec4aa3dedb0ad6c0b8efa: ('Connection broken: IncompleteRead(0 bytes read, 8244261 more expected)', IncompleteRead(0 bytes read, 8244261 more expected))
[2025-08-02 09:12:47,769: ERROR/ForkPoolWorker-4] Upload task 2365 failed: Failed to download original file
[2025-08-02 09:12:48,556: ERROR/ForkPoolWorker-4] Task app.upload_task[webdav_upload_2365] raised unexpected: Exception('Failed to download original file')
Traceback (most recent call last):
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 ~~~^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           ~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/app/app.py", line 265, in upload_task
    raise e
  File "/app/app.py", line 217, in upload_task
    raise Exception("Failed to download original file")
Exception: Failed to download original file
[2025-08-02 09:12:52,353: ERROR/ForkPoolWorker-1] Failed to download original file for 8e3ebaf84a49087e3dc24a8c06e9db7a87ee5ee338f13623b98bb28a317347e3: ('Connection broken: IncompleteRead(0 bytes read, 7866963 more expected)', IncompleteRead(0 bytes read, 7866963 more expected))
[2025-08-02 09:12:52,353: ERROR/ForkPoolWorker-1] Upload task 2366 failed: Failed to download original file
[2025-08-02 09:12:53,146: ERROR/ForkPoolWorker-1] Task app.upload_task[webdav_upload_2366] raised unexpected: Exception('Failed to download original file')
Traceback (most recent call last):
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 ~~~^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           ~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/app/app.py", line 265, in upload_task
    raise e
  File "/app/app.py", line 217, in upload_task
    raise Exception("Failed to download original file")
Exception: Failed to download original file
[2025-08-02 09:12:54,277: ERROR/ForkPoolWorker-2] Failed to download original file for 6da03104f1abb6f373665ade589a60efdddbac87b634d76ef2b09acd52409321: ('Connection broken: IncompleteRead(0 bytes read, 7226618 more expected)', IncompleteRead(0 bytes read, 7226618 more expected))
[2025-08-02 09:12:54,277: ERROR/ForkPoolWorker-2] Upload task 2367 failed: Failed to download original file
[2025-08-02 09:12:55,086: ERROR/ForkPoolWorker-2] Task app.upload_task[webdav_upload_2367] raised unexpected: Exception('Failed to download original file')
Traceback (most recent call last):
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 ~~~^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           ~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/app/app.py", line 265, in upload_task
    raise e
  File "/app/app.py", line 217, in upload_task
    raise Exception("Failed to download original file")
Exception: Failed to download original file
[2025-08-02 09:12:59,716: ERROR/ForkPoolWorker-4] Failed to download original file for efc7cbe5242d1f29f644b2d0f5f8b25d71472c6c09cb7900a9b6db2ef44c5bfe: ('Connection broken: IncompleteRead(0 bytes read, 7089751 more expected)', IncompleteRead(0 bytes read, 7089751 more expected))
[2025-08-02 09:12:59,716: ERROR/ForkPoolWorker-4] Upload task 2368 failed: Failed to download original file
[2025-08-02 09:13:00,473: ERROR/ForkPoolWorker-4] Task app.upload_task[webdav_upload_2368] raised unexpected: Exception('Failed to download original file')
Traceback (most recent call last):
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 ~~~^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           ~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/app/app.py", line 265, in upload_task
    raise e
  File "/app/app.py", line 217, in upload_task
    raise Exception("Failed to download original file")
Exception: Failed to download original file
[2025-08-02 09:13:02,314: ERROR/ForkPoolWorker-1] Failed to download original file for 080b0f011d001c9e0bd3ac15501fd5b35a0db8dd5442ba24b34d5d916ac12e81: ('Connection broken: IncompleteRead(7737 bytes read, 9024059 more expected)', IncompleteRead(7737 bytes read, 9024059 more expected))
[2025-08-02 09:13:02,314: ERROR/ForkPoolWorker-1] Upload task 2369 failed: Failed to download original file
[2025-08-02 09:13:03,130: ERROR/ForkPoolWorker-1] Task app.upload_task[webdav_upload_2369] raised unexpected: Exception('Failed to download original file')
Traceback (most recent call last):
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 ~~~^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           ~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/app/app.py", line 265, in upload_task
    raise e
  File "/app/app.py", line 217, in upload_task
    raise Exception("Failed to download original file")
Exception: Failed to download original file
[2025-08-02 09:13:17,772: ERROR/ForkPoolWorker-2] Failed to download original file for 711c8ecfeb7bff004b389d7ae267c70714028c0df25894417e899383254fcc49: ('Connection broken: IncompleteRead(0 bytes read, 7314461 more expected)', IncompleteRead(0 bytes read, 7314461 more expected))
[2025-08-02 09:13:17,772: ERROR/ForkPoolWorker-2] Upload task 2370 failed: Failed to download original file
[2025-08-02 09:13:18,570: ERROR/ForkPoolWorker-2] Task app.upload_task[webdav_upload_2370] raised unexpected: Exception('Failed to download original file')
Traceback (most recent call last):
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 ~~~^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           ~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/app/app.py", line 265, in upload_task
    raise e
  File "/app/app.py", line 217, in upload_task
    raise Exception("Failed to download original file")
Exception: Failed to download original file
[2025-08-02 09:13:21,858: ERROR/ForkPoolWorker-4] Failed to download original file for 74575addc35484170fb0c7497faa91da51e1a8a5365585c6a5b05cc13144ad1d: ('Connection broken: IncompleteRead(0 bytes read, 8665997 more expected)', IncompleteRead(0 bytes read, 8665997 more expected))
[2025-08-02 09:13:21,858: ERROR/ForkPoolWorker-4] Upload task 2371 failed: Failed to download original file
[2025-08-02 09:13:22,682: ERROR/ForkPoolWorker-4] Task app.upload_task[webdav_upload_2371] raised unexpected: Exception('Failed to download original file')
Traceback (most recent call last):
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 ~~~^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           ~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/app/app.py", line 265, in upload_task
    raise e
  File "/app/app.py", line 217, in upload_task
    raise Exception("Failed to download original file")
Exception: Failed to download original file
[2025-08-02 09:13:26,948: ERROR/ForkPoolWorker-1] Failed to download original file for 52988fa7a75f91d9c0717aa0e5116e346024c09f2493b0ba9e53ec0909896fe5: ('Connection broken: IncompleteRead(0 bytes read, 8837737 more expected)', IncompleteRead(0 bytes read, 8837737 more expected))
[2025-08-02 09:13:26,948: ERROR/ForkPoolWorker-1] Upload task 2372 failed: Failed to download original file
[2025-08-02 09:13:27,703: ERROR/ForkPoolWorker-1] Task app.upload_task[webdav_upload_2372] raised unexpected: Exception('Failed to download original file')
Traceback (most recent call last):
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 ~~~^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           ~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/app/app.py", line 265, in upload_task
    raise e
  File "/app/app.py", line 217, in upload_task
    raise Exception("Failed to download original file")
Exception: Failed to download original file
[2025-08-02 09:13:31,012: ERROR/ForkPoolWorker-2] Failed to download original file for e713ddfa82873fd966e5e678257d5a0201f6084d715ecf6b4b265516f703e253: ('Connection broken: IncompleteRead(0 bytes read, 8713367 more expected)', IncompleteRead(0 bytes read, 8713367 more expected))
[2025-08-02 09:13:31,012: ERROR/ForkPoolWorker-2] Upload task 2373 failed: Failed to download original file
[2025-08-02 09:13:31,876: ERROR/ForkPoolWorker-2] Task app.upload_task[webdav_upload_2373] raised unexpected: Exception('Failed to download original file')
Traceback (most recent call last):
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 ~~~^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           ~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/app/app.py", line 265, in upload_task
    raise e
  File "/app/app.py", line 217, in upload_task
    raise Exception("Failed to download original file")
Exception: Failed to download original file
[2025-08-02 15:49:34,431: ERROR/ForkPoolWorker-2] Failed to download original file for e3f86ec2f6ace2e7976f9cf8a07cc5999a288a1f508eb7148ed2180cda7d1b69: ('Connection broken: IncompleteRead(0 bytes read, 8092872 more expected)', IncompleteRead(0 bytes read, 8092872 more expected))
[2025-08-02 15:49:34,431: ERROR/ForkPoolWorker-2] Upload task 2374 failed: Failed to download original file
[2025-08-02 15:49:35,223: ERROR/ForkPoolWorker-2] Task app.upload_task[webdav_upload_2374] raised unexpected: Exception('Failed to download original file')
Traceback (most recent call last):
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 ~~~^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           ~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/app/app.py", line 265, in upload_task
    raise e
  File "/app/app.py", line 217, in upload_task
    raise Exception("Failed to download original file")
Exception: Failed to download original file
[2025-08-02 15:50:49,737: ERROR/ForkPoolWorker-2] Failed to download original file for 38313a0ad35caa13b2bb3134f8f09261e5bf77f4a58462f272471053f474b668: ('Connection broken: IncompleteRead(0 bytes read, 5669075 more expected)', IncompleteRead(0 bytes read, 5669075 more expected))
[2025-08-02 15:50:49,738: ERROR/ForkPoolWorker-2] Upload task 2375 failed: Failed to download original file
[2025-08-02 15:50:50,529: ERROR/ForkPoolWorker-2] Task app.upload_task[webdav_upload_2375] raised unexpected: Exception('Failed to download original file')
Traceback (most recent call last):
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 ~~~^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           ~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/app/app.py", line 265, in upload_task
    raise e
  File "/app/app.py", line 217, in upload_task
    raise Exception("Failed to download original file")
Exception: Failed to download original file
[2025-08-03 00:42:18,418: ERROR/ForkPoolWorker-2] Failed to download original file for 54896f2c37a406ea98a04f26ff25459ea5afafa309d71444fb765a9940abcb25: ('Connection broken: IncompleteRead(0 bytes read, 6773983 more expected)', IncompleteRead(0 bytes read, 6773983 more expected))
[2025-08-03 00:42:18,419: ERROR/ForkPoolWorker-2] Upload task 2376 failed: Failed to download original file
[2025-08-03 00:42:19,209: ERROR/ForkPoolWorker-2] Task app.upload_task[webdav_upload_2376] raised unexpected: Exception('Failed to download original file')
Traceback (most recent call last):
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 ~~~^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           ~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/app/app.py", line 265, in upload_task
    raise e
  File "/app/app.py", line 217, in upload_task
    raise Exception("Failed to download original file")
Exception: Failed to download original file
[2025-08-03 00:43:17,028: ERROR/ForkPoolWorker-2] Failed to download original file for 54896f2c37a406ea98a04f26ff25459ea5afafa309d71444fb765a9940abcb25: ('Connection broken: IncompleteRead(0 bytes read, 6773983 more expected)', IncompleteRead(0 bytes read, 6773983 more expected))
[2025-08-03 00:43:17,028: ERROR/ForkPoolWorker-2] Upload task 2377 failed: Failed to download original file
[2025-08-03 00:43:17,825: ERROR/ForkPoolWorker-2] Task app.upload_task[webdav_upload_2377] raised unexpected: Exception('Failed to download original file')
Traceback (most recent call last):
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 ~~~^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           ~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/app/app.py", line 265, in upload_task
    raise e
  File "/app/app.py", line 217, in upload_task
    raise Exception("Failed to download original file")
Exception: Failed to download original file
[2025-08-03 00:43:34,378: ERROR/ForkPoolWorker-2] Failed to download original file for 54896f2c37a406ea98a04f26ff25459ea5afafa309d71444fb765a9940abcb25: ('Connection broken: IncompleteRead(0 bytes read, 6773983 more expected)', IncompleteRead(0 bytes read, 6773983 more expected))
[2025-08-03 00:43:34,378: ERROR/ForkPoolWorker-2] Upload task 2378 failed: Failed to download original file
[2025-08-03 00:43:35,215: ERROR/ForkPoolWorker-2] Task app.upload_task[webdav_upload_2378] raised unexpected: Exception('Failed to download original file')
Traceback (most recent call last):
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 ~~~^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           ~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/app/app.py", line 265, in upload_task
    raise e
  File "/app/app.py", line 217, in upload_task
    raise Exception("Failed to download original file")
Exception: Failed to download original file
[2025-08-03 00:48:12,888: ERROR/ForkPoolWorker-2] Failed to download original file for 1cb45299ce4f185d5757cc9209eb7d567e2cac43f45d803c00f771108fe0bb31: ('Connection broken: IncompleteRead(0 bytes read, 7953433 more expected)', IncompleteRead(0 bytes read, 7953433 more expected))
[2025-08-03 00:48:12,889: ERROR/ForkPoolWorker-2] Upload task 2379 failed: Failed to download original file
[2025-08-03 00:48:13,718: ERROR/ForkPoolWorker-2] Task app.upload_task[webdav_upload_2379] raised unexpected: Exception('Failed to download original file')
Traceback (most recent call last):
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 ~~~^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           ~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/app/app.py", line 265, in upload_task
    raise e
  File "/app/app.py", line 217, in upload_task
    raise Exception("Failed to download original file")
Exception: Failed to download original file
[2025-08-03 00:48:21,475: ERROR/ForkPoolWorker-2] Failed to download original file for 1cb45299ce4f185d5757cc9209eb7d567e2cac43f45d803c00f771108fe0bb31: ('Connection broken: IncompleteRead(0 bytes read, 7953433 more expected)', IncompleteRead(0 bytes read, 7953433 more expected))
[2025-08-03 00:48:21,475: ERROR/ForkPoolWorker-2] Upload task 2380 failed: Failed to download original file
[2025-08-03 00:48:22,266: ERROR/ForkPoolWorker-2] Task app.upload_task[webdav_upload_2380] raised unexpected: Exception('Failed to download original file')
Traceback (most recent call last):
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 ~~~^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           ~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/app/app.py", line 265, in upload_task
    raise e
  File "/app/app.py", line 217, in upload_task
    raise Exception("Failed to download original file")
Exception: Failed to download original file
[2025-08-03 07:26:55,267: ERROR/ForkPoolWorker-2] Failed to download original file for dd15d7457e0e1e849e8e2310f834a538ed619d721d372ee77f938e816b5af6cd: ('Connection broken: IncompleteRead(0 bytes read, 8811604 more expected)', IncompleteRead(0 bytes read, 8811604 more expected))
[2025-08-03 07:26:55,268: ERROR/ForkPoolWorker-2] Upload task 2381 failed: Failed to download original file
[2025-08-03 07:26:56,021: ERROR/ForkPoolWorker-2] Task app.upload_task[webdav_upload_2381] raised unexpected: Exception('Failed to download original file')
Traceback (most recent call last):
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 ~~~^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           ~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/app/app.py", line 265, in upload_task
    raise e
  File "/app/app.py", line 217, in upload_task
    raise Exception("Failed to download original file")
Exception: Failed to download original file
[2025-08-03 13:29:51,059: ERROR/ForkPoolWorker-2] Failed to download original file for 462b6179ab752c207eb37a4e3e6713de70d4bb03316c949dae883e45a66795a4: ('Connection broken: IncompleteRead(0 bytes read, 9688818 more expected)', IncompleteRead(0 bytes read, 9688818 more expected))
[2025-08-03 13:29:51,059: ERROR/ForkPoolWorker-2] Upload task 2382 failed: Failed to download original file
[2025-08-03 13:29:51,866: ERROR/ForkPoolWorker-2] Task app.upload_task[webdav_upload_2382] raised unexpected: Exception('Failed to download original file')
Traceback (most recent call last):
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 ~~~^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           ~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/app/app.py", line 265, in upload_task
    raise e
  File "/app/app.py", line 217, in upload_task
    raise Exception("Failed to download original file")
Exception: Failed to download original file
