import hashlib
import json

from django.contrib.auth.decorators import login_required
from django.shortcuts import get_object_or_404
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils.translation import gettext_lazy as _
from django.core.paginator import Paginator
from django.http import JsonResponse

from onemusic_backend.settings import DOWNLOADER_SECRET, DOWNLOAD_SERVER_URL
from .models import UserDownloadedMusic, Music, RedemptionRecord, Profile, RedeemCode
from .utils import generate_secure_hash_salt


@login_required
def user_downloaded_music(request):
    """
    获取当前用户已下载的歌曲列表（分页，每页15条）
    """
    user = request.user
    page_number = request.GET.get('page', 1)  # 获取请求中的页码，默认为第1页
    page_size = 15  # 每页15条数据

    downloaded_songs = UserDownloadedMusic.objects.filter(user=user).order_by('-downloaded_at').select_related('music')

    # 创建分页对象
    paginator = Paginator(downloaded_songs, page_size)
    page_obj = paginator.get_page(page_number)
    # 处理数据
    music_list = []
    for item in page_obj:
        # 生成安全的hash_salt并使用新的缩略图链接格式
        pic_hash_salt = generate_secure_hash_salt(item.music.partition, item.music.hash, 'webp')
        thumbnail_url = f"{DOWNLOAD_SERVER_URL}/{item.music.partition}/{item.music.hash}/webp?salt={pic_hash_salt}"

        music_data = {
            "song_hash": hashlib.sha256(f"{item.music.title}{item.music.album}{item.music.artist}id{DOWNLOADER_SECRET}".encode('utf-8')).hexdigest(),
            "title": item.music.title,
            'videoId': 'id',
            "artist": item.music.artist,
            "album": item.music.album,
            "thumbnail": thumbnail_url
        }
        music_list.append(music_data)

    # 返回分页信息
    return JsonResponse({
        "downloaded_music": music_list,
        "total_pages": paginator.num_pages,
        "current_page": page_obj.number,
        "has_next": page_obj.has_next(),
        "has_previous": page_obj.has_previous(),
    }, safe=False)


@csrf_exempt
@login_required
@require_http_methods(["DELETE"])
def delete_downloaded_music(request, title, artist, album):
    """
    删除当前用户指定的已下载歌曲
    """
    user = request.user  # 获取当前用户
    music_hash = hashlib.md5(
        (title + album + artist).encode('utf-8')
    ).hexdigest()
    music = get_object_or_404(Music, hash=music_hash)  # 获取指定歌曲

    # 查找是否有该用户的下载记录
    download_record = UserDownloadedMusic.objects.filter(user=user, music=music).first()

    if not download_record:
        return JsonResponse({"error": _( "下载记录未找到" )}, status=404)

    # 删除下载记录
    download_record.delete()

    return JsonResponse({"message": _( "下载记录删除成功" )}, status=200)


from django.http import JsonResponse
from django.core.paginator import Paginator
from .models import UploadTask

@csrf_exempt
@login_required
def user_upload_tasks(request):
    """
    获取当前用户上传任务列表（分页，每页15条）
    """
    user = request.user
    page_number = request.GET.get('page', 1)  # 获取请求中的页码，默认为第1页
    page_size = 15  # 每页15条数据

    upload_tasks = UploadTask.objects.filter(user=user).order_by('-id').select_related('music')

    # 创建分页对象
    paginator = Paginator(upload_tasks, page_size)
    page_obj = paginator.get_page(page_number)

    # 处理数据
    task_list = [
        {
            "task_id": task.id,
            "music_title": task.music.title,
            "music_artist": task.music.artist,
            "status": task.status,
            "detail": task.reason,
        }
        for task in page_obj
    ]

    return JsonResponse({
        "upload_tasks": task_list,
        "total_pages": paginator.num_pages,
        "current_page": page_obj.number,
        "has_next": page_obj.has_next(),
        "has_previous": page_obj.has_previous(),
    }, safe=False)

@login_required
@csrf_exempt
@require_http_methods(["DELETE"])
def delete_upload_task(request, task_id):
    """
    删除当前用户指定的上传任务
    """
    user = request.user
    task = get_object_or_404(UploadTask, id=task_id, user=user)  # 只能删除自己的任务

    # 删除任务
    task.delete()

    return JsonResponse({"message": _( "上传任务删除成功" )}, status=200)


