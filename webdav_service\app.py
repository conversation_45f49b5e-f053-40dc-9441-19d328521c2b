import os
import tempfile
import shutil
from flask import Flask, request, jsonify
from flask_cors import CORS
from celery import Celery
import logging

from config import (
    WEBDAV_SECRET_KEY, WEBDAV_JWT_SECRET, BACKEND_URL, DOWNLOAD_SERVER_URL,
    CELERY_BROKER_URL, CELERY_RESULT_BACKEND, LOG_LEVEL, LOG_FORMAT
)
from utils import (
    verify_webdav_config_signature, test_webdav_connection,
    download_original_file, transcode_file, upload_to_webdav,
    cleanup_temp_files, verify_webdav_jwt, download_cover_from_url,
    update_task_status
)
import requests

app = Flask(__name__)
CORS(app, supports_credentials=True, origins=["https://1music.cc","https://1music-frontend.vercel.app"])

# 配置日志
logging.basicConfig(level=getattr(logging, LOG_LEVEL), format=LOG_FORMAT)
logger = logging.getLogger(__name__)

# 初始化Celery
celery = Celery('app', broker=CELERY_BROKER_URL)
celery.conf.update(
    result_backend=CELERY_RESULT_BACKEND,
    task_routes={
        'upload_task': {'queue': 'webdav_upload'}
    }
)

@app.route('/add_config', methods=['POST'])
def add_config():
    """
    添加WebDAV配置接口 - 用户携带JWT请求
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({'success': False, 'error': 'No JSON data provided'}), 400

        # 验证JWT
        jwt_token = data.get('jwt_token')
        if not jwt_token:
            return jsonify({'success': False, 'error': 'Missing JWT token'}), 400

        is_valid, payload = verify_webdav_jwt(jwt_token)
        if not is_valid:
            return jsonify({'success': False, 'error': 'Invalid JWT token'}), 401

        required_fields = ['url', 'username', 'password']
        if not all(field in data for field in required_fields):
            return jsonify({'success': False, 'error': 'Missing required fields'}), 400

        # 测试WebDAV连接
        is_valid, error_message = test_webdav_connection(
            data['url'],
            data['username'],
            data['password']
        )

        if not is_valid:
            return jsonify({
                'success': False,
                'error': f'WebDAV connection failed: {error_message}'
            }), 400

        # 连接成功，向后端发送添加配置回调
        callback_payload = {
            'secret': WEBDAV_SECRET_KEY,
            'jwt_token': jwt_token,
            'url': data['url'],
            'username': data['username'],
            'password': data['password']
        }

        response = requests.post(
            f"{BACKEND_URL}webdav_add_config_callback/",
            json=callback_payload,
            timeout=10
        )

        if response.status_code == 200:
            result = response.json()
            return jsonify({
                'success': True,
                'message': 'WebDAV configuration added successfully',
                'config_id': result.get('config_id'),
                'signature': result.get('signature')
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to add configuration to backend'
            }), 500

    except Exception as e:
        logger.error(f"Error in add_config: {str(e)}")
        return jsonify({'success': False, 'error': 'Internal server error'}), 500

@app.route('/upload', methods=['POST'])
def upload_music():
    """
    接收上传请求并启动Celery任务 - 用户直接发送歌曲信息和webdav配置
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': 'No JSON data provided'}), 400

        # 验证必要字段
        required_fields = ['song_hash', 'song_title', 'song_artist', 'album',
                          'format', 'webdav_config', 'cover_url', 'video_id', 'jwt_token']

        if not all(field in data for field in required_fields):
            return jsonify({'error': 'Missing required fields'}), 400

        # 验证JWT
        jwt_token = data.get('jwt_token')
        if not jwt_token:
            return jsonify({'error': 'Missing JWT token'}), 400

        is_valid, payload = verify_webdav_jwt(jwt_token)
        if not is_valid:
            return jsonify({'error': 'Invalid JWT token'}), 401

        # 验证webdav配置签名
        webdav_config = data['webdav_config']
        if 'signature' not in webdav_config:
            return jsonify({'error': 'Missing webdav config signature'}), 400

        # 验证签名
        config_data = {
            'config_id': webdav_config['id'],
            'url': webdav_config['url'],
            'username': webdav_config['username'],
            'password': webdav_config['password']
        }

        if not verify_webdav_config_signature(config_data, webdav_config['signature']):
            return jsonify({'error': 'Invalid webdav config signature'}), 403

        # 先在Django后端创建UploadTask记录
        create_task_payload = {
            'secret': WEBDAV_SECRET_KEY,
            'jwt_token': jwt_token,
            'song_title': data['song_title'],
            'song_artist': data['song_artist'],
            'album': data['album'],
            'format_type': data['format'],
            'webdav_config_id': webdav_config['id']
        }

        response = requests.post(
            f"{BACKEND_URL}create_upload_task/",
            json=create_task_payload,
            timeout=10
        )

        if response.status_code != 200:
            logger.error(f"Failed to create upload task: {response.status_code} - {response.text}")
            return jsonify({'error': 'Failed to create upload task'}), 500

        # 获取Django后端返回的任务ID
        task_response = response.json()
        task_id = task_response['task_id']

        # 启动Celery上传任务
        upload_task.apply_async(
            args=[data, task_id],
            task_id=f"webdav_upload_{task_id}"
        )

        return jsonify({'success': True, 'message': 'Upload task started', 'task_id': task_id})

    except Exception as e:
        logger.error(f"Error in upload_music: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@celery.task(bind=True,queue='webdav_upload')
def upload_task(self, data, task_id):
    """
    Celery上传任务
    """
    song_hash = data['song_hash']
    song_title = data['song_title']
    song_artist = data['song_artist']
    album = data['album']
    video_id = data['video_id']
    format_type = data['format']
    webdav_config = data['webdav_config']
    cover_url = data['cover_url']
    
    temp_dir = None
    
    try:
        # 创建临时工作目录
        temp_dir = tempfile.mkdtemp(prefix=f"webdav_upload_{self.request.id}_")
        logger.info(f"Created temp directory: {temp_dir}")

        logger.info(f"Starting upload task for {song_title} by {song_artist}")

        # 1. 从下载服务器获取原始文件（参考前端逻辑）
        logger.info(f"Downloading original file for {song_title} by {song_artist}")
        original_file_path = download_original_file(
            song_title, song_artist, album, video_id, song_hash, temp_dir,cover_url
        )

        if not original_file_path:
            raise Exception("Failed to download original file")

        # 2. 下载封面图片
        logger.info(f"Downloading cover image from URL: {cover_url}")
        cover_image_path = download_cover_from_url(cover_url, temp_dir)

        # 3. 转码文件（参考前端逻辑）
        logger.info(f"Transcoding file to {format_type}")
        metadata = {
            'title': song_title,
            'artist': song_artist,
            'album': album
        }

        transcoded_file_path = transcode_file(
            original_file_path,
            format_type,
            temp_dir,
            cover_image_path=cover_image_path,
            metadata=metadata
        )

        if not transcoded_file_path:
            raise Exception("Failed to transcode file")

        # 4. 上传到WebDAV
        logger.info(f"Uploading to WebDAV: {webdav_config['url']}")
        upload_to_webdav(
            transcoded_file_path,
            webdav_config,
            song_title,
            song_artist,
            album,
            format_type
        )

        logger.info(f"Upload task {task_id} completed successfully")

        # 更新任务状态为成功
        update_task_status(task_id, 'success')

    except Exception as e:
        error_msg = str(e)
        logger.error(f"Upload task {task_id} failed: {error_msg}")

        # 更新任务状态为失败
        update_task_status(task_id, 'failed', error_msg)

        raise e
        
    finally:
        # 清理临时目录
        if temp_dir and os.path.exists(temp_dir):
            try:
                shutil.rmtree(temp_dir)
                logger.info(f"Cleaned up temp directory: {temp_dir}")
            except Exception as e:
                logger.error(f"Failed to cleanup temp directory {temp_dir}: {str(e)}")



if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5001, debug=True)
