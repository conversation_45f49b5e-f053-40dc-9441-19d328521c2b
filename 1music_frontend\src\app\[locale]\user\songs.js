import React, {useState, useEffect, useContext} from "react";
import {
    Box,
    IconButton,
    Pagination,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Typography,
    Stack,
    Dialog,
    DialogTitle,
    CircularProgress,
    DialogContent,
    RadioGroup,
    FormControlLabel,
    Radio,
    DialogActions,
    Button
} from "@mui/material";
import DownloadIcon from "@mui/icons-material/Download";
import DeleteIcon from "@mui/icons-material/Delete";
import {backendUrl, webdavServiceUrl} from "@/src/app/[locale]/config";
import {useNotifications} from "@toolpad/core";
import {useTranslations} from "next-intl";

import {GlobalContext} from "@/src/app/[locale]/user/page";

const DownloadDialog = ({ open, onClose, song }) => {
    const [format, setFormat] = useState('mp3');
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const [selectedConfig, setSelectedConfig] = useState(null);
    const [selectedConfigId, setSelectedConfigId] = useState(null)
    const notifications = useNotifications();
    const { webdavConfigs, webdavJwt } = useContext(GlobalContext)
    const t = useTranslations("Download")

    const handleDownload = () => {
        // Validate song data before opening download page
        if (!song || !song.title || !song.album || !song.artist || !song.videoId || !song.song_hash) {
            alert(t("incomplete_song_info"));
            return;
        }

        // Open new tab with query string
        const queryParams = new URLSearchParams({
            title: song.title,
            album: song.album,
            artist: song.artist,
            videoId: song.videoId,
            request_format: format,
            song_hash: song.song_hash,
            thumbnail: song.thumbnail || '' // thumbnail might be optional
        }).toString();

        window.open(`/download?${queryParams}`, '_blank');

        onClose();
    };

    const handleWebDavUpload = async () => {
        if (!selectedConfigId) {
            alert(t("select_webdav_config"));
            return;
        }

        if (!song || !song.title || !song.album || !song.artist || !song.videoId) {
            alert(t("incomplete_song_info"));
            return;
        }

        setIsLoading(true);
        setError(null);

        // 找到选中的WebDAV配置
        // 将字符串转换为数字进行比较，因为后端返回的id是数字类型
        const selectedWebdavConfig = webdavConfigs.find(config => config.id === parseInt(selectedConfigId));
        if (!selectedWebdavConfig) {
            alert(t("webdav_config_not_found"));
            setIsLoading(false);
            return;
        }

        // 构造WebDAV服务需要的数据
        const uploadData = {
            song_hash: song.song_hash,
            song_title: song.title,
            song_artist: song.artist,
            album: song.album,
            video_id: song.videoId,
            format: format,
            cover_url: song.thumbnail, // 直接使用封面URL
            jwt_token: webdavJwt, // 添加JWT令牌
            webdav_config: {
                id: selectedWebdavConfig.id,
                url: selectedWebdavConfig.url,
                username: selectedWebdavConfig.username,
                password: selectedWebdavConfig.password,
                signature: selectedWebdavConfig.signature
            }
        };

        try {
            // 直接调用WebDAV服务
            const response = await fetch(webdavServiceUrl + 'upload', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(uploadData),
                credentials:'include'
            });

            const result = await response.json();
            if (!response.ok) {
                throw new Error(result.error || t("upload_error"));
            }

            notifications.show(t("upload_success", {title: song.title}), {
                autoHideDuration: 2000
            });
            setIsLoading(false);
            onClose();
        } catch (err) {
            setError(err.message);
            setIsLoading(false);
        }
    };

    const handleWebDavConfigChange = (e) => {
        const configId = e.target.value;
        // 将字符串转换为数字进行比较，因为后端返回的id是数字类型
        const selected = webdavConfigs.find(config => config.id === parseInt(configId));
        setSelectedConfig(selected)
        setSelectedConfigId(configId);
    };

    return (
        <Dialog onClick={event => event.stopPropagation()} open={open} onClose={onClose}>
            <DialogTitle>{t("download_song",{title: song.title})}</DialogTitle>
            {isLoading? <Box display="flex" justifyContent="center" alignItems="center" height='80px'>
                <CircularProgress />
            </Box> : <DialogContent>
                <RadioGroup value={format} onChange={(e) => setFormat(e.target.value)}>
                    <FormControlLabel value="mp3" control={<Radio />} label="MP3" />
                    <FormControlLabel value="flac" control={<Radio />} label="FLAC" />
                </RadioGroup>
                {webdavConfigs.length > 0 && (
                    <>
                        <Typography variant="subtitle1">{t("select_webdav_config")}</Typography>
                        <RadioGroup value={selectedConfigId || ''} onChange={handleWebDavConfigChange}>
                            {webdavConfigs.map((config, index) => (
                                <FormControlLabel key={index} value={config.id} control={<Radio />} label={config.url} />
                            ))}
                        </RadioGroup>
                    </>
                )}
                {error && <Typography color="error">{error}</Typography>}
            </DialogContent>}
            <DialogActions>
                <Button onClick={handleDownload} disabled={isLoading}>{t("download")}</Button>
                <Button onClick={handleWebDavUpload} disabled={isLoading || webdavConfigs.length < 1}>{t("upload_to_webdav")}</Button>
                <Button onClick={onClose} disabled={isLoading}>{t("cancel")}</Button>
            </DialogActions>
        </Dialog>
    );
};


const SongsPage = () => {
    const [downloadedSongs, setDownloadedSongs] = useState([]);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [dialogOpen, setDialogOpen] = useState([...Array(30)].map(() => false))

    const fetchSong = (page) => {
        fetch(`${backendUrl}user_downloaded_music/?page=${page}`,{
            credentials:'include'
        })
            .then(response => response.json())
            .then(data => {
                setDownloadedSongs(data.downloaded_music);
                setTotalPages(data.total_pages);
            });
    }

    useEffect(() => {
        fetchSong(currentPage)
    }, [currentPage]);

    const handlePageChange = (_, value) => {
        setCurrentPage(value);
    };

    const handleDownload = (index) => {
        setDialogOpen(prev => {
            const newState = [...prev];
            newState[index] = true;
            return newState;
        });
    };

    const handleDelete = (song) => {
        fetch(`${backendUrl}downloaded-music/${song.title}/${song.album}/${song.artist}/delete/`, {
            method: "DELETE",
            headers: {
                "Content-Type": "application/json",
            },
            credentials:'include'
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error("Failed to delete download record");
                }
                return response.json();
            })
            .then(() => {
                fetchSong(currentPage); // Refresh the page data after deletion
            })
            .catch(error => {
                console.error("Error deleting music:", error);
            });
    };


    return (
        <Box p={0}>
            <TableContainer>
                <Table>
                    <TableHead>
                        <TableRow>
                            <TableCell sx={{ width: '33.3%', wordWrap: 'break-word' }}>Title</TableCell>
                            <TableCell sx={{ width: '33.3%', wordWrap: 'break-word' }}>Album</TableCell>
                            <TableCell sx={{ width: '33.3%', wordWrap: 'break-word' }}>Artist</TableCell>
                            <TableCell sx={{ width: '10%' }} align="center">Actions</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {downloadedSongs.map((song, index) => (
                            <TableRow key={index}>
                                <DownloadDialog open={dialogOpen[index]} onClose={() => setDialogOpen([...Array(30)].map(() => false))} song={song} />
                                <TableCell sx={{ wordWrap: 'break-word' }}>{song.title}</TableCell>
                                <TableCell sx={{ wordWrap: 'break-word' }}>{song.album}</TableCell>
                                <TableCell sx={{ wordWrap: 'break-word' }}>{song.artist}</TableCell>
                                <TableCell align="center">
                                    <Stack direction="row" spacing={1} justifyContent="center">
                                        <IconButton onClick={() => handleDownload(index)} color='inherit'>
                                            <DownloadIcon />
                                        </IconButton>
                                        <IconButton color="error" onClick={() => handleDelete(song)}>
                                            <DeleteIcon />
                                        </IconButton>
                                    </Stack>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </TableContainer>
            {downloadedSongs.length > 0 ? <Pagination
                count={totalPages}
                page={currentPage}
                onChange={handlePageChange}
                color="primary"
                sx={{ mt: 3 }}
            /> : <Typography variant="h6" color="textSecondary" sx={{ mt: 3, textAlign: 'center' }}>
                No downloaded records
            </Typography>}
        </Box>
    );
};

export default SongsPage;
